# create a serializer for the Company model
from accounts.models import User
from accounts.serializers import UserSerializer
from company.models import Insurance
from company.models import Licenses
from company.serializers import InsuranceSerializer
from company.serializers import LicensesSerializer
from contacts.models import Contact
from contacts.models import Subcontractor
from contacts.serializers import ContactSerializer
from contacts.serializers import SubcontractSerializer
from core.serializers import TimezoneConverterMixin
from django.conf import settings
from django.core.exceptions import MultipleObjectsReturned
from django.core.exceptions import ValidationError
from django.shortcuts import get_object_or_404
from drf_extra_fields.fields import Base64ImageField
from general.serializers import InFavoriteSerializerMixin
from resources.models import Tag
from resources.serializers import TagSerializer
from rest_framework import serializers
from rest_framework.request import Request
from storage.models import File
from storage.serializers import FileSerializer

from .models import ChangeOrder
from .models import DocumentCategoryAccess
from .models import Project
from .models import ProjectDocument
from .models import ProjectInvite
from .models import ProjectShare


class InvitedUserSerializer(
    TimezoneConverterMixin, serializers.ModelSerializer
):
    class Meta:
        model = User
        fields = [
            "id",
            "email",
            "profile_picture",
            "first_name",
            "last_name",
            "mobile",
        ]


class DocumentCategoryAccessSerializer(
    TimezoneConverterMixin, serializers.ModelSerializer
):
    class Meta:
        model = DocumentCategoryAccess
        fields = ["document_category", "access_level"]


class BaseProjectSerializer(
    TimezoneConverterMixin,
    InFavoriteSerializerMixin,
    serializers.ModelSerializer,
):

    is_invited = serializers.SerializerMethodField()
    is_owner = serializers.SerializerMethodField()

    class Meta:
        model = Project
        read_only_fields = (
            "created",
            "updated",
            "created_by",
        )
        exclude = (
            "is_deleted",
            "created_by",
        )

    def get_is_invited(self, obj: Project):
        request: Request = self.context.get("request")

        if not request:
            return False

        user: User = request.user

        if obj.created_by.id == user.id:
            return False

        return obj.projectinvite_set.filter(user=user).exists()

    def get_is_owner(self, obj: Project):
        request: Request = self.context.get("request")

        if not request:
            return False

        user: User = request.user

        return obj.created_by.id == user.id


class ProjectListSerializer(BaseProjectSerializer):
    pass


class ProjectSerializer(
    TimezoneConverterMixin,
    InFavoriteSerializerMixin,
    serializers.ModelSerializer,
):
    created_by = UserSerializer(read_only=True)
    subcontractors = SubcontractSerializer(many=True, read_only=True)
    additional_contacts = ContactSerializer(many=True, read_only=True)
    new_additional_contacts = ContactSerializer(
        many=True, required=False, write_only=True
    )
    additional_contacts_ids = serializers.ListField(
        write_only=True, required=False
    )
    invited_users = serializers.SerializerMethodField()
    document_category_accesses = serializers.SerializerMethodField()
    shared_with = serializers.SerializerMethodField()
    project_logo = Base64ImageField(required=False)
    is_shared = serializers.SerializerMethodField()

    class Meta:
        model = Project
        read_only_fields = ("created", "updated", "created_by")
        exclude = ("is_deleted",)

    def create(self, validated_data):
        validated_data["created_by"] = self.context["request"].user

        new_additional_contacts_data = validated_data.pop(
            "new_additional_contacts", []
        )
        additional_contacts_ids = validated_data.pop(
            "additional_contacts_ids", []
        )

        project_instance = super().create(validated_data)

        self._handle_additional_contacts(
            project_instance,
            new_additional_contacts_data,
            additional_contacts_ids,
        )

        return project_instance

    def update(self, instance, validated_data):
        validated_data["created_by"] = self.context["request"].user

        new_additional_contacts_data = validated_data.pop(
            "new_additional_contacts", []
        )
        additional_contacts_ids = validated_data.pop(
            "additional_contacts_ids", []
        )

        # Update instance fields with validated data
        instance = super().update(instance, validated_data)

        self._handle_additional_contacts(
            instance, new_additional_contacts_data, additional_contacts_ids
        )

        return instance

    def _handle_additional_contacts(
        self, instance, new_additional_contacts_data, additional_contacts_ids
    ):
        # Remove existing additional contacts not in the updated list
        instance.additional_contacts.exclude(
            id__in=additional_contacts_ids
        ).delete()

        # Create new additional contacts and associate them with the project
        new_additional_contacts = [
            Contact.objects.create(
                **contact_data, uploaded_by=instance.created_by
            )
            for contact_data in new_additional_contacts_data
        ]
        instance.additional_contacts.add(*new_additional_contacts)

        # Add additional contacts by IDs
        additional_contacts_to_add = Contact.objects.filter(
            id__in=additional_contacts_ids
        )
        instance.additional_contacts.add(*additional_contacts_to_add)

    def get_invited_users(self, obj: Project):
        request = self.context.get("request")
        if not request:
            return []

        user = request.user
        if obj.created_by.id != user.id:
            return []

        invited_users = obj.projectinvite_set.all()
        # Pass context when creating new serializer
        return ProjectInviteSerializer(
            invited_users, many=True, context=self.context
        ).data

    def get_shared_with(self, obj: Project):
        request: Request = self.context.get("request")

        if not request:
            return []

        user: User = request.user

        if obj.created_by.id != user.id:
            return []

        shared_records = ProjectShare.objects.filter(project=obj)
        shared_emails = set()
        for record in shared_records:
            shared_emails.update(record.user_emails)
        return list(shared_emails)

    def get_document_category_accesses(self, obj: Project):
        request: Request = self.context.get("request")

        if not request:
            return []

        user: User = request.user

        if obj.created_by.id == user.id:
            owner_access = [
                {
                    "document_category": category,
                    "access_level": DocumentCategoryAccess.AccessLevel.WRITE,
                }
                for category in DocumentCategoryAccess.DocumentCategory.ALL
            ]
            return owner_access

        invite = obj.projectinvite_set.filter(user=user).first()
        if not invite:
            return []

        document_category_accesses = DocumentCategoryAccess.objects.filter(
            project_invite=invite
        )

        return DocumentCategoryAccessSerializer(
            document_category_accesses, many=True, context=self.context
        ).data

    def get_is_shared(self, obj: Project):
        request: Request = self.context.get("request")

        if not request:
            return False

        user: User = request.user

        if obj.created_by.id == user.id:
            return False

        is_shared = obj.projectshare_set.filter(
            user_emails__contains=user.email
        ).exists()
        return is_shared


class ProjectDocumentSerializer(
    TimezoneConverterMixin, serializers.ModelSerializer
):
    visibility = serializers.ChoiceField(
        choices=ProjectDocument.Visibility.CHOICES,
        default=ProjectDocument.Visibility.PUBLIC,
    )
    tags = serializers.SerializerMethodField()
    tag_names = serializers.ListField(
        child=serializers.CharField(), write_only=True, required=False
    )
    file = serializers.SerializerMethodField()
    file_id = serializers.CharField(write_only=True, required=False)
    subcontractor_obj = SubcontractSerializer(
        source="subcontractor", read_only=True
    )

    class Meta:
        model = ProjectDocument
        read_only_fields = ("created", "updated", "project")
        exclude = ("is_deleted",)

    def create(self, validated_data):
        request: Request = self.context["request"]
        user: User = request.user

        validated_data[
            "tags"
        ] = Tag.objects.create_tags_from_serializer_validated_data(
            validated_data, user
        )

        try:
            project_id = request.parser_context["kwargs"]["project_id"]
            validated_data["project"] = get_object_or_404(
                Project, pk=project_id
            )

            file_id = validated_data.get("file_id", None)
            validated_data["file"] = (
                get_object_or_404(File, pk=file_id) if file_id else None
            )
        except ValidationError as exc:
            raise serializers.ValidationError(
                detail="Invalid request"
            ) from exc

        project_document = super().create(validated_data)

        self.update_or_create_thumbnail(project_document)

        return project_document

    def update(self, instance: ProjectDocument, validated_data: dict):
        user: User = self.context["request"].user

        validated_data[
            "tags"
        ] = Tag.objects.create_tags_from_serializer_validated_data(
            validated_data, user
        )

        file_id = validated_data.get("file_id", None)
        if file_id == "" and instance.file:
            instance.file.is_deleted = True
            instance.file.save()

        self.update_or_create_thumbnail(instance)
        return super().update(instance, validated_data)

    def update_or_create_thumbnail(self, obj: ProjectDocument):
        if not obj.file:
            return

        thumbnail_url = obj.file.thumbnail_url
        if not thumbnail_url and obj.file.thumbnail:
            try:
                thumbnail_url = obj.file.thumbnail.url
            except ValueError:
                return

        if thumbnail_url and obj.thumbnail != thumbnail_url:
            obj.thumbnail = thumbnail_url
            obj.save(update_fields=['thumbnail'])

    def get_tags(self, obj: ProjectDocument):
        return TagSerializer(obj.tags.all(), many=True).data

    def get_file(self, obj: ProjectDocument):
        if not obj.file or obj.file.is_deleted:
            return None

        return FileSerializer(obj.file).data


class ListProjectDocumentSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProjectDocument
        read_only_fields = ("created", "updated", "project")
        exclude = ("is_deleted",)


class ProjectSubcontractorSerializer(
    TimezoneConverterMixin, serializers.ModelSerializer
):
    class Operation:
        ADD = "add"
        REMOVE = "remove"
        CHOICES = ((ADD, ADD), (REMOVE, REMOVE))

    subcontractor_ids = serializers.ListField(
        child=serializers.UUIDField(), write_only=True, required=False
    )
    subcontractor_data = SubcontractSerializer(
        many=True, write_only=True, required=False
    )
    subcontractors = SubcontractSerializer(many=True, read_only=True)
    operation = serializers.ChoiceField(
        choices=Operation.CHOICES, write_only=True, required=True
    )

    def update(self, instance: Project, validated_data: dict):
        subcontractor_ids = validated_data.pop("subcontractor_ids", [])
        subcontractor_data = validated_data.pop("subcontractor_data", [])
        operation = validated_data["operation"]

        # Handle direct subcontractor IDs
        for subcontractor_id in subcontractor_ids:
            subcontractor = Subcontractor.objects.filter(
                id=subcontractor_id
            ).first()
            if subcontractor:
                self._handle_operation(instance, subcontractor, operation)

        # Handle subcontractor data
        for data in subcontractor_data:
            contact_id = data.get("contact_id")
            contact_data = data.get("contact")

            # Find existing contact or create new one
            contact = None
            if contact_id:
                if isinstance(contact_id, Contact):
                    contact = contact_id
                else:
                    contact = Contact.objects.filter(id=contact_id).first()
            elif contact_data:
                serializer = ContactSerializer(
                    data=contact_data, context=self.context
                )
                serializer.is_valid(raise_exception=True)
                contact = Contact.objects.create(
                    **serializer.validated_data,
                    uploaded_by=self.context["request"].user,
                )

            if not contact:
                continue

            # Find or create subcontractor
            subcontractor = Subcontractor.objects.filter(
                contact=contact
            ).first()

            if subcontractor:
                self._update_subcontractor_details(subcontractor, data)
            else:
                # Prepare data for subcontractor creation
                subcontractor_data = {
                    k: v
                    for k, v in data.items()
                    if k not in ["contact", "contact_id"]
                }
                subcontractor_data["contact_id"] = contact.id

                serializer = SubcontractSerializer(
                    data=subcontractor_data, context=self.context
                )
                serializer.is_valid(raise_exception=True)
                subcontractor = serializer.save()

            self._handle_operation(instance, subcontractor, operation)

        instance.save()
        return super().update(instance, validated_data)

    def _update_subcontractor_details(self, subcontractor, data):
        user = self.context["request"].user
        company = user.company_set.first()
        company_id = company.id if company else None

        # Handle certificates if provided
        if "certificates_ids" in data and data.get("certificates_ids"):
            file_ids = data.get("certificates_ids", [])
            subcontract_serializer = SubcontractSerializer(
                context=self.context
            )
            subcontract_serializer.create_subcontractor_certificate(
                subcontractor, file_ids
            )

        # Handle licenses if provided
        if "licenses" in data and data.get("licenses"):
            licenses_data = data.get("licenses", [])
            # Create proper context for license operations
            license_context = {
                "request": self.context.get("request"),
                "view": type(
                    "obj", (), {"kwargs": {"company_id": company_id}}
                ),
            }

            for license_data in licenses_data:
                license_id = license_data.get("id")
                if license_id:
                    # Update existing license
                    try:
                        license_instance = Licenses.objects.get(id=license_id)
                        serializer = LicensesSerializer(
                            license_instance,
                            data=license_data,
                            context=license_context,
                            partial=True,
                        )
                        serializer.is_valid(raise_exception=True)
                        serializer.save()
                    except Licenses.DoesNotExist as e:
                        raise ValidationError(
                            f"License with id {license_id} does not exist"
                        ) from e
                else:
                    # Create new license
                    license_data.update(
                        {
                            "owner_contact": subcontractor.id,
                            "company_id": company_id,
                        }
                    )
                    serializer = LicensesSerializer(
                        data=license_data, context=license_context
                    )
                    serializer.is_valid(raise_exception=True)
                    serializer.save()

        # Handle insurances if provided
        if "insurances" in data and data.get("insurances"):
            insurances_data = data.get("insurances", [])
            # Create proper context for insurance operations
            insurance_context = {
                "request": self.context.get("request"),
                "view": type(
                    "obj", (), {"kwargs": {"company_id": company_id}}
                ),
            }

            for insurance_data in insurances_data:
                insurance_id = insurance_data.get("id")
                if insurance_id:
                    # Update existing insurance
                    try:
                        insurance_instance = Insurance.objects.get(
                            id=insurance_id
                        )
                        serializer = InsuranceSerializer(
                            insurance_instance,
                            data=insurance_data,
                            context=insurance_context,
                            partial=True,
                        )
                        serializer.is_valid(raise_exception=True)
                        serializer.save()
                    except Insurance.DoesNotExist as e:
                        raise ValidationError(
                            f"Insurance with id {insurance_id} does not exist"
                        ) from e
                else:
                    # Create new insurance
                    insurance_data.update(
                        {
                            "owner_contact": subcontractor.id,
                            "company_id": company_id,
                        }
                    )
                    serializer = InsuranceSerializer(
                        data=insurance_data, context=insurance_context
                    )
                    serializer.is_valid(raise_exception=True)
                    serializer.save()

        # Update other fields
        for key, value in data.items():
            if key not in [
                "certificates_ids",
                "licenses",
                "insurances",
                "contact",
                "contact_id",
            ]:
                setattr(subcontractor, key, value)

        subcontractor.save()

    def _handle_operation(
        self, project: Project, subcontractor: Subcontractor, operation: str
    ):
        if operation == self.Operation.ADD:
            project.subcontractors.add(subcontractor)
        elif operation == self.Operation.REMOVE:
            project.subcontractors.remove(subcontractor)

    class Meta:
        model = Project
        fields = (
            "subcontractors",
            "subcontractor_ids",
            "subcontractor_data",
            "operation",
        )


class ProjectDetailSerializer(
    TimezoneConverterMixin, serializers.ModelSerializer
):
    created_by = UserSerializer()

    class Meta:
        model = Project
        fields = [
            "id",
            "name",
            "project_logo",
            "owner",
            "contact",
            "email",
            "status",
            "created_by",
        ]


class ProjectInviteSerializer(
    TimezoneConverterMixin, serializers.ModelSerializer
):
    user = serializers.SerializerMethodField()
    document_category_accesses = DocumentCategoryAccessSerializer(many=True)
    invitee_emails = serializers.ListField(
        child=serializers.EmailField(), write_only=True, required=False
    )
    project_object = ProjectDetailSerializer(source="project", read_only=True)

    class Meta:
        model = ProjectInvite
        fields = [
            "id",
            "user",
            "status",
            "document_category_accesses",
            "invitee_emails",
            "project",
            "project_object",
            "created_at",
        ]
        read_only_fields = [
            "project",
            "status",
        ]

    def get_user(self, obj):

        if isinstance(obj, ProjectInvite):
            if self.context.get("view_type") == "received":
                user = obj.project.created_by

                # Pass context when creating new serializer
                return (
                    InvitedUserSerializer(user, context=self.context).data
                    if user
                    else None
                )
            else:
                user = obj.user
                return (
                    InvitedUserSerializer(user, context=self.context).data
                    if user
                    else None
                )
        return None

    def validate_invitee_emails(self, value):
        if len(value) > 10:
            raise serializers.ValidationError(
                "Cannot invite more than 10 users at once."
            )

        current_user_email = self.context["request"].user.email
        if current_user_email in value:
            raise serializers.ValidationError(
                "Cannot share a project with yourself."
            )

        users = []
        user_status = []  # Track if each user is newly created or existing
        for email in value:
            user, created = User.objects.get_or_create(email=email)
            users.append(user)
            user_status.append(
                created
            )  # Append True if new user, False if existing

        # Store user statuses in the context for use in perform_create
        self.context["user_status"] = user_status

        return users

    def create(self, validated_data):
        document_category_accesses_data = validated_data.pop(
            "document_category_accesses"
        )
        invitee_users = validated_data.pop("invitee_emails")

        # List of all document categories
        all_document_categories = [
            choice[0]
            for choice in DocumentCategoryAccess.DocumentCategory.CHOICES
        ]

        if not invitee_users:
            raise serializers.ValidationError(
                "No valid invitee emails provided."
            )

        invitee_user = invitee_users[
            0
        ]  # We're now creating one invite at a time
        try:
            project_invite, created = ProjectInvite.objects.get_or_create(
                project=validated_data["project"],
                user=invitee_user,
                defaults=validated_data,
            )
        except MultipleObjectsReturned:
            project_invites = ProjectInvite.objects.filter(
                project=validated_data["project"], user=invitee_user
            )
            project_invite = project_invites.first()
            project_invites = project_invites.exclude(pk=project_invite.pk)
            for pi in project_invites:
                # Merge document category accesses and delete duplicate invites
                self._merge_document_category_accesses(project_invite, pi)
                pi.delete()

        if created:
            self._create_or_update_document_category_accesses(
                project_invite,
                document_category_accesses_data,
                all_document_categories,
            )

        return project_invite

    def update(self, instance, validated_data):
        document_category_accesses_data = validated_data.pop(
            "document_category_accesses", []
        )

        # Update the status
        instance.status = validated_data.get("status", instance.status)
        instance.save()

        # List of all document categories
        all_document_categories = [
            choice[0]
            for choice in DocumentCategoryAccess.DocumentCategory.CHOICES
        ]

        self._create_or_update_document_category_accesses(
            instance, document_category_accesses_data, all_document_categories
        )

        return instance

    def _create_or_update_document_category_accesses(
        self,
        project_invite,
        document_category_accesses_data,
        all_document_categories,
    ):
        # Keep track of passed categories
        passed_categories = set()

        for access_data in document_category_accesses_data:
            passed_categories.add(access_data["document_category"])
            DocumentCategoryAccess.objects.update_or_create(
                project_invite=project_invite,
                document_category=access_data["document_category"],
                defaults={"access_level": access_data["access_level"]},
            )

        # Add default no_access entries for categories not passed
        for category in all_document_categories:
            if category not in passed_categories:
                DocumentCategoryAccess.objects.update_or_create(
                    project_invite=project_invite,
                    document_category=category,
                    defaults={
                        "access_level": DocumentCategoryAccess.AccessLevel.NO_ACCESS
                    },
                )


class ProjectInviteStatusSerializer(
    TimezoneConverterMixin, serializers.ModelSerializer
):
    class Meta:
        model = ProjectInvite
        fields = ["status"]


class ProjectShareSerializer(
    TimezoneConverterMixin, serializers.ModelSerializer
):
    document_category_accesses = DocumentCategoryAccessSerializer(many=True)
    user_emails = serializers.ListField(
        child=serializers.EmailField(), required=True
    )
    link = serializers.SerializerMethodField()

    class Meta:
        model = ProjectShare
        fields = [
            "id",
            "project",
            "permission_id",
            "document_category_accesses",
            "user_emails",
            "link",
        ]
        read_only_fields = ["id", "project", "permission_id"]

    def validate_user_emails(self, value):
        if len(value) > 10:
            raise serializers.ValidationError(
                "Cannot share with more than 10 users at once."
            )
        if self.context["request"].user.email in value:
            raise serializers.ValidationError(
                "Cannot share a project with yourself."
            )
        return value

    def get_link(self, obj: ProjectShare):

        return f"{settings.FRONTEND_URL}?permissionId={obj.permission_id}"

    def create(self, validated_data):
        project = self.context["project"]
        document_category_accesses_data = validated_data.pop(
            "document_category_accesses"
        )
        user_emails = validated_data.pop("user_emails")

        # Generate a unique permission_id based on project_id and document_category_accesses
        permission_id = self._generate_permission_id(
            project.id, document_category_accesses_data
        )

        # Check if a ProjectShare instance with the same project and permission_id exists
        project_share = ProjectShare.objects.filter(
            project=project, permission_id=permission_id
        ).first()

        if project_share:
            # Merge emails and save existing ProjectShare instance
            existing_emails = set(project_share.user_emails)
            new_emails = set(user_emails)
            if existing_emails != new_emails:
                project_share.user_emails = list(existing_emails | new_emails)
                project_share.save()
        else:
            # Create a new ProjectShare instance
            project_share = ProjectShare.objects.create(
                project=project,
                permission_id=permission_id,
                user_emails=user_emails,
            )

        # Create or update document category accesses
        self._create_or_update_document_category_accesses(
            project_share, document_category_accesses_data
        )

        return project_share

    def update(self, instance, validated_data):
        document_category_accesses_data = validated_data.pop(
            "document_category_accesses", []
        )
        new_user_emails = set(
            validated_data.pop("user_emails", instance.user_emails)
        )

        # Generate a new permission_id
        new_permission_id = self._generate_permission_id(
            instance.project.id, document_category_accesses_data
        )

        if new_permission_id != instance.permission_id:
            # Check for an existing ProjectShare with the new permission_id
            conflicting_project_share = ProjectShare.objects.filter(
                project=instance.project, permission_id=new_permission_id
            ).first()

            if conflicting_project_share:
                # Merge emails from the instance being updated into the conflicting instance
                conflicting_project_share.user_emails = list(
                    set(conflicting_project_share.user_emails)
                    | new_user_emails
                )
                conflicting_project_share.save()

                # Remove the original instance if emails were successfully merged
                instance.delete()
                return conflicting_project_share

            # Update the permission_id if no conflict exists
            instance.permission_id = new_permission_id

        # Update other fields if any
        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        instance.user_emails = list(new_user_emails)
        instance.save()

        # Update or create document category accesses
        self._create_or_update_document_category_accesses(
            instance, document_category_accesses_data
        )

        return instance

    def _create_or_update_document_category_accesses(
        self, project_share, document_category_accesses_data
    ):
        # Keep track of passed categories
        passed_categories = set()

        for access_data in document_category_accesses_data:
            passed_categories.add(access_data["document_category"])
            DocumentCategoryAccess.objects.update_or_create(
                project_share=project_share,
                document_category=access_data["document_category"],
                defaults={"access_level": access_data["access_level"]},
            )

        # Get all possible document categories
        all_categories = DocumentCategoryAccess.DocumentCategory.ALL

        # Add default no_access entries for categories not passed
        for category in all_categories:
            if category not in passed_categories:
                DocumentCategoryAccess.objects.update_or_create(
                    project_share=project_share,
                    document_category=category,
                    defaults={
                        "access_level": DocumentCategoryAccess.AccessLevel.NO_ACCESS
                    },
                )

    def _generate_permission_id(self, project_id, document_category_accesses):
        import hashlib

        hash_input = f"{project_id}:" + ",".join(
            sorted(
                f"{access['document_category']}:{access['access_level']}"
                for access in document_category_accesses
            )
        )
        return hashlib.md5(hash_input.encode()).hexdigest()


class ProjectPermissionSerializer(
    TimezoneConverterMixin,
    InFavoriteSerializerMixin,
    serializers.ModelSerializer,
):
    created_by = UserSerializer(read_only=True)
    subcontractors = SubcontractSerializer(many=True, read_only=True)
    additional_contacts = ContactSerializer(many=True, read_only=True)
    document_category_accesses = serializers.SerializerMethodField()
    project_logo = Base64ImageField(required=False)
    documents = serializers.SerializerMethodField()

    class Meta:
        model = Project
        read_only_fields = ("created", "updated", "created_by")
        exclude = ("is_deleted",)

    def get_document_category_accesses(self, obj: Project):
        request = self.context.get("request")
        permission_id = self.context.get("permission_id")

        if not request or not permission_id:
            return []

        project_share = ProjectShare.objects.filter(
            project=obj, permission_id=permission_id
        ).first()
        if not project_share:
            return []

        document_category_accesses = DocumentCategoryAccess.objects.filter(
            project_share=project_share
        )

        return DocumentCategoryAccessSerializer(
            document_category_accesses, many=True
        ).data

    def get_documents(self, obj: Project):
        request = self.context.get("request")
        permission_id = self.context.get("permission_id")

        if not request or not permission_id:
            return []

        project_share = ProjectShare.objects.filter(
            project=obj, permission_id=permission_id
        ).first()
        if not project_share:
            return []

        # Get accessible document categories
        accessible_categories = (
            DocumentCategoryAccess.objects.filter(project_share=project_share)
            .exclude(access_level="no_access")
            .values_list("document_category", flat=True)
        )

        # Filter documents based on accessible categories
        documents = ProjectDocument.objects.filter(
            project=obj, category__in=accessible_categories
        )

        return ProjectDocumentSerializer(documents, many=True).data


class ChangeOrderSerializer(
    TimezoneConverterMixin, serializers.ModelSerializer
):
    class Meta:
        model = ChangeOrder
        read_only_fields = ("created", "updated", "project")
        exclude = ("is_deleted",)
