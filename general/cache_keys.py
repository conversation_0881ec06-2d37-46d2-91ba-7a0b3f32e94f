class REDIS_CACHE_KEY:
    @staticmethod
    def get_project_key(collaborators: list):
        sorted_collaborators = sorted(collaborators)
        return f"project_key_{':'.join(sorted_collaborators)}"

    @staticmethod
    def get_recent_activities_key(user_id: str):
        return f"recent_activities_key_{user_id}"

    @staticmethod
    def get_notifications_key(user_id: str):
        return f"notifications_key_{user_id}"

    @staticmethod
    def get_email_verification_key(user_id: str):
        return f"email_verification_key_{user_id}"

    @staticmethod
    def get_contact_key(user_id: str):
        return f"contact_key_{user_id}"

    @staticmethod
    def get_insurance_key(user_id: str, company_id: str):
        return f"insurance_key_{user_id}_{company_id}"

    @staticmethod
    def get_license_key(user_id: str, company_id: str):
        return f"license_key_{user_id}_{company_id}"
