from backoffice.service import AnalyticsService
from company.services import CompanyService
from contacts.services import ContactService
from core.core_services.sendgrid_service import CoreService
from core.core_services.sendgrid_service import SendGridService
from general.service import GeneralService
from notifications.services import NotificationService
from project.services import ProjectService
from recent_app.services import RecentActivityService
from storage.thumbnail_service import ThumbnailGeneratorService


class SERVICE_NAMES:
    AnalyticsService = "analytics_service"
    CompanyService = "company_service"
    ContactService = "contact_service"
    ProjectService = "project_service"
    RecentActivityService = "recent_activity_service"
    NotificationService = "notification_service"
    ThumbnailGeneratorService = "thumbnail_generator_service"
    CoreService = "core_service"
    GeneralService = "general_service"


class ServiceLocator:
    service = {}

    analytics_service: AnalyticsService
    company_service: CompanyService
    contact_service: ContactService
    project_service: ProjectService
    recent_activity_service: RecentActivityService
    notification_service: NotificationService
    thumbnail_generator_service: ThumbnailGeneratorService
    core_service: CoreService
    general_service: GeneralService

    def __init__(self):
        self._services = {}

    def register(self, name, service):
        self._services[name] = service

    def get(self, name):
        return self._services[name]

    def __getitem__(self, name):
        return self.get(name)

    def __getattr__(self, name):
        return self.get(name)


#  register services


service_locator = ServiceLocator()


sendgrid_service = SendGridService()
service_locator.register(
    SERVICE_NAMES.CoreService, CoreService(sendgrid_service=sendgrid_service)
)

service_locator.register(SERVICE_NAMES.AnalyticsService, AnalyticsService())
service_locator.register(SERVICE_NAMES.CompanyService, CompanyService())
service_locator.register(SERVICE_NAMES.ContactService, ContactService())
service_locator.register(SERVICE_NAMES.ProjectService, ProjectService())
service_locator.register(
    SERVICE_NAMES.RecentActivityService, RecentActivityService()
)
service_locator.register(
    SERVICE_NAMES.NotificationService, NotificationService()
)

service_locator.register(
    SERVICE_NAMES.ThumbnailGeneratorService, ThumbnailGeneratorService()
)
service_locator.register(SERVICE_NAMES.GeneralService, GeneralService())
