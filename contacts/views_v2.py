import logging

from company.models import Company
from contacts.models import Contact
from contacts.serializers import ContactSerializer
from contacts.serializers import ListContactSerializer
from contacts.views import CreateListContactView
from rest_framework.response import Response

logger = logging.getLogger(__name__)


class CreateListContactViewV2(CreateListContactView):
    def get_serializer_class(self):
        if self.request.method == "GET":
            return ListContactSerializer
        return ContactSerializer

    def get_queryset(self):
        """Override get_queryset to implement caching for contacts."""
        user = self.request.user

        try:
            # Try to get cached contacts first
            from core.dependency_injection import service_locator

            cached_contacts = (
                service_locator.contact_service.get_cached_contacts(user)
            )
            if cached_contacts is not None:
                logger.debug(
                    f"Cache hit for contact queryset - user: {user.id}"
                )
                return cached_contacts
        except Exception as e:
            # Clear potentially corrupted cache
            try:
                from core.dependency_injection import service_locator

                service_locator.contact_service.clear_contact_cache(user)
            except Exception as clear_error:
                logger.error(
                    f"Failed to clear corrupted contact cache: {clear_error}"
                )
            logger.warning(
                f"Error retrieving cached contacts, falling back to DB: {e}"
            )

        # Cache miss - get fresh data
        logger.debug(f"Cache miss for contact queryset - user: {user.id}")
        return self._get_and_cache_queryset(user)

    def _get_fresh_queryset(self, user):
        """Get fresh queryset without caching."""
        current_company = Company.objects.get_user_current_company(user)
        return Contact.objects.get_contacts_uploaded_by_user(
            current_company.user
        )

    def _get_and_cache_queryset(self, user):
        """Get fresh queryset and cache it."""
        queryset = self._get_fresh_queryset(user)

        # Cache the results for future use
        try:
            from core.dependency_injection import service_locator

            service_locator.contact_service.cache_contacts(user, queryset)
            logger.debug(f"Cached contact queryset - user: {user.id}")
        except Exception as e:
            logger.warning(f"Failed to cache contact queryset: {e}")

        return queryset

    def list(self, request, *args, **kwargs):
        """Override list method to implement response caching."""
        from core.dependency_injection import service_locator

        # Check if caching is disabled
        if request.query_params.get("disable_cache"):
            return self._get_uncached_response(request, *args, **kwargs)

        # Try to get cached response with comprehensive error handling
        (
            cached_response,
            _,
        ) = service_locator.contact_service.get_cached_contact_response_with_fallback(
            request
        )
        if cached_response is not None:
            return Response(cached_response)

        # Cache miss - get fresh data
        logger.debug(f"Cache miss for contact list - user: {request.user.id}")
        return self._get_and_cache_response(
            request, service_locator, *args, **kwargs
        )

    def _get_uncached_response(self, request, *args, **kwargs):
        """Get response without caching."""
        return super().list(request, *args, **kwargs)

    def _get_and_cache_response(
        self, request, service_locator, *args, **kwargs
    ):
        """Get response and cache it."""
        response = super().list(request, *args, **kwargs)

        # Only cache successful responses
        if response.status_code == 200:
            service_locator.contact_service.cache_contact_response_with_logging(
                request, response.data
            )

        return response
