from accounts.models import User
from core.models import <PERSON>Model, BaseAddress
from django.db import models
from django.db.models import QuerySet
from django.db.models.functions import Lower
from project.models import Project
from storage.models import File


class ContactManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False)

    def get_contacts_uploaded_by_user(self, user):
        return (
            self.get_queryset()
            .filter(uploaded_by=user)
            .order_by(Lower("first_name"))
        )


class Contact(BaseModel, BaseAddress):
    subcontractor: "Subcontractor"

    objects = ContactManager()

    profile_picture = models.ImageField(
        upload_to="contacts", blank=True, null=True
    )
    first_name = models.CharField(
        max_length=255,
    )
    last_name = models.CharField(
        max_length=255, default="", blank=True, null=True
    )
    email = models.EmailField(null=True, blank=True, default="")

    phone_number = models.Char<PERSON>ield(
        max_length=255, null=True, blank=True, default=""
    )
    extension = models.CharField(
        max_length=5, null=True, blank=True, default=""
    )
    company = models.CharField(
        max_length=255, null=True, blank=True, default=""
    )
    uploaded_by = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="contacts"
    )


class SubcontractorManager(models.Manager):
    def get_queryset(self) -> QuerySet["Subcontractor"]:
        return super().get_queryset().filter(is_deleted=False)

    def get_subcontractor_added_by_user(self, user: User):
        return (
            self.get_queryset()
            .filter(contact__uploaded_by=user)
            .order_by(Lower("contact__first_name"))
        )

    def get_user_subcontractors(self, user: "User"):
        subcontractors = (
            self.get_queryset()
            .filter(contact__uploaded_by=user)
            .order_by(Lower("contact__first_name"))
        )

        return subcontractors

    def get_subcontractor_by_project(self, project: Project):
        subcontractor_ids = (
            SubContractorEstimate.objects.filter(project=project)
            .order_by(Lower("contact__first_name"))
            .values_list("subcontractor_id", flat=True)
        )

        return (
            self.get_user_subcontractors(project.created_by)
            .filter(id__in=subcontractor_ids)
            .order_by(Lower("contact__first_name"))
        )


class Subcontractor(BaseModel):
    estimates: models.QuerySet["SubContractorEstimate"]
    certificates: models.QuerySet["SubcontractorCertificate"]
    SubcontractorCertificate_set: models.QuerySet["SubcontractorCertificate"]
    tax_documents: "SubcontractorCertificate"

    objects = SubcontractorManager()
    contact = models.OneToOneField(Contact, on_delete=models.CASCADE)
    has_tax_documents = models.BooleanField(default=False)
    tax_id = models.CharField(max_length=1024, default="")

    def __str__(self) -> str:
        return str(self.contact.uploaded_by)


class SubcontractorCertificateManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False)

    def get_subcontractor_certificates(self, contractor: Subcontractor):
        return (
            self.get_queryset()
            .filter(
                contractor=contractor,
            )
            .order_by("-created_at")
        )


class SubcontractorCertificate(BaseModel):
    class FileCategory:
        COMPENSATION = "compensation"
        INSURANCE = "insurance"
        TAX_DOCUMENT = "tax_document"

        ALL = (COMPENSATION, INSURANCE, TAX_DOCUMENT)

        CHOICES = (
            (COMPENSATION, "compensation"),
            (INSURANCE, "insurance"),
            (TAX_DOCUMENT, "tax_document"),
        )

    objects = SubcontractorCertificateManager()
    contractor = models.ForeignKey(
        Subcontractor, on_delete=models.CASCADE, related_name="certificates"
    )
    certificate = models.ForeignKey(
        File,
        related_name="subcontractor_certificates",
        on_delete=models.CASCADE,
    )
    file_category = models.CharField(
        max_length=255, choices=FileCategory.CHOICES
    )


class SubContractorEstimate(BaseModel):
    project = models.ForeignKey(
        Project,
        on_delete=models.CASCADE,
        related_name="subcontractor_estimates",
    )
    subcontractor = models.ForeignKey(
        Subcontractor, related_name="estimates", on_delete=models.CASCADE
    )
    estimates = models.ManyToManyField(
        File, related_name="subcontractor_estimates", blank=True
    )
