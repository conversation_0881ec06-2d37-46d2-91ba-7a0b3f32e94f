import hashlib
import logging

from accounts.models import User
from django.conf import settings
from django.core.cache import cache
from general.cache_keys import REDIS_CACHE_KEY

logger = logging.getLogger(__name__)


class ContactService:
    CACHE_VERSION = "v2"

    def _generate_cache_key(
        self, user_id, query_params=None, key_type="list", contact_id=None
    ):
        """Generate a consistent cache key with collision resistance."""
        # Sort query params for consistent hashing
        if query_params:
            sorted_params = sorted(query_params.items())
            params_str = "&".join(f"{k}={v}" for k, v in sorted_params)
            params_hash = hashlib.md5(params_str.encode()).hexdigest()[:8]
        else:
            params_hash = "default"

        # Include contact_id in cache key if provided
        if contact_id:
            return f"contact_{key_type}_{self.CACHE_VERSION}_{user_id}_{contact_id}_{params_hash}"
        else:
            return f"contact_{key_type}_{self.CACHE_VERSION}_{user_id}_{params_hash}"

    def cache_contact_response(self, request, response_data):
        """Cache contact list response with improved error handling."""
        if not response_data:
            return

        try:
            cache_key = self._generate_cache_key(
                request.user.id, request.query_params
            )
            cache.set(cache_key, response_data, settings.CONTACT_CACHE_TIMEOUT)
        except Exception as e:
            logger.warning(f"Failed to cache contact response: {e}")

    def get_cached_contact_response(self, request):
        """Retrieve cached contact list response."""
        try:
            cache_key = self._generate_cache_key(
                request.user.id, request.query_params
            )
            return cache.get(cache_key)
        except Exception as e:
            logger.warning(f"Failed to retrieve cached contact response: {e}")
            return None

    def get_cached_contact_response_with_fallback(self, request):
        """
        Retrieve cached contact response with comprehensive error handling and cache corruption cleanup.

        Returns:
            tuple: (cached_data, cache_status)
            - cached_data: The cached response data or None if not found/error
            - cache_status: 'hit', 'miss', or 'error'
        """
        try:
            cached_data = self.get_cached_contact_response(request)
            if cached_data is not None:
                logger.debug(
                    f"Cache hit for contact list - user: {request.user.id}"
                )
                return cached_data, "hit"
            else:
                logger.debug(
                    f"Cache miss for contact list - user: {request.user.id}"
                )
                return None, "miss"
        except Exception as e:
            # Clear potentially corrupted cache
            try:
                self.clear_contact_response_cache(request.user)
            except Exception as clear_error:
                logger.error(
                    f"Failed to clear corrupted contact cache: {clear_error}"
                )
            logger.warning(
                f"Error retrieving cached contacts, falling back to DB: {e}"
            )
            return None, "error"

    def cache_contact_response_with_logging(self, request, response_data):
        """Cache contact response with detailed logging."""
        try:
            self.cache_contact_response(request, response_data)
            logger.debug(f"Cached contact list - user: {request.user.id}")
        except Exception as e:
            logger.warning(f"Failed to cache contact response: {e}")

    def cache_contacts(self, user: User, contacts_data):
        """Cache contact queryset data."""
        try:
            key = REDIS_CACHE_KEY.get_contact_key(str(user.id))
            cache.set(key, contacts_data, settings.CONTACT_CACHE_TIMEOUT)
        except Exception as e:
            logger.warning(f"Failed to cache contacts: {e}")

    def get_cached_contacts(self, user: User):
        """Retrieve cached contact queryset data."""
        try:
            key = REDIS_CACHE_KEY.get_contact_key(str(user.id))
            return cache.get(key)
        except Exception as e:
            logger.warning(f"Failed to retrieve cached contacts: {e}")
            return None

    def clear_contact_cache(self, user: User):
        """Clear contact cache with improved efficiency and error handling."""
        try:
            # Clear queryset cache
            key = REDIS_CACHE_KEY.get_contact_key(str(user.id))
            cache.delete(key)

            # Clear response cache
            self.clear_contact_response_cache(user)

            logger.debug(f"Cleared contact cache - user: {user.id}")
        except Exception as e:
            logger.warning(f"Failed to clear contact cache: {e}")

    def clear_contact_response_cache(self, user: User):
        """Clear contact response cache for a user."""
        try:
            if hasattr(cache, "delete_pattern"):
                # Use pattern deletion if available (Redis)
                pattern = f"contact_list_{self.CACHE_VERSION}_{user.id}_*"
                cache.delete_pattern(pattern)
            else:
                # Fallback for other cache backends
                # Try to delete common cache keys
                base_pattern = f"contact_list_{self.CACHE_VERSION}_{user.id}_"
                common_keys = [f"{base_pattern}default"]

                for key in common_keys:
                    try:
                        cache.delete(key)
                    except Exception as e:
                        logger.warning(
                            f"Failed to delete cache key {key}: {e}"
                        )

                logger.info(
                    f"Pattern deletion not available, some contact caches may persist for user {user.id}"
                )

        except Exception as e:
            logger.warning(f"Failed to clear contact response cache: {e}")

    def cache_contact_detail(self, request, response_data, contact_id):
        """Cache contact detail response."""
        if not response_data:
            return

        try:
            cache_key = self._generate_cache_key(
                request.user.id, key_type=f"detail_{contact_id}"
            )
            cache.set(cache_key, response_data, settings.CONTACT_CACHE_TIMEOUT)
        except Exception as e:
            logger.warning(f"Failed to cache contact detail response: {e}")

    def get_cached_contact_detail(self, request, contact_id):
        """Retrieve cached contact detail response."""
        try:
            cache_key = self._generate_cache_key(
                request.user.id, key_type=f"detail_{contact_id}"
            )
            return cache.get(cache_key)
        except Exception as e:
            logger.warning(
                f"Failed to retrieve cached contact detail response: {e}"
            )
            return None

    def cache_contact_detail_with_logging(
        self, request, response_data, contact_id
    ):
        """Cache contact detail with detailed logging."""
        try:
            self.cache_contact_detail(request, response_data, contact_id)
            logger.debug(
                f"Cached contact detail - contact: {contact_id}, user: {request.user.id}"
            )
        except Exception as e:
            logger.warning(f"Failed to cache contact detail: {e}")
