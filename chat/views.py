import logging
import uuid
from datetime import datetime
from typing import Any

from accounts.models import User
from chat.serializers import AttachmentSerializer
from chat.serializers import ChatSerializers
from chat.serializers import CreateAttachmentSerializer
from chat.serializers import ParticipantCreateAsListSerializer
from chat.serializers import ParticipantSerializer
from chat.serializers import RoomSerializer
from chat.services.ktg_chat_client import Chat<PERSON><PERSON>
from chat.services.ktg_chat_client import ChatClientConfig
from contacts.models import Contact
from django.conf import settings
from django.db.models import Q
from django.shortcuts import get_object_or_404
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from general.models import Setting
from notifications.mixins import NotificationCleanupMixin
from notifications.models import Notification
from project.models import Project
from rest_framework import status
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from .models import ProjectRoom
from .serializers import ProjectRoomSerializer
from .tasks import process_chat_notifications


logger = logging.getLogger(__name__)


class ChatViewSet(NotificationCleanupMixin, viewsets.ViewSet):
    permission_classes = [IsAuthenticated]

    def __init__(self, **kwargs: Any) -> None:
        super().__init__(**kwargs)
        self.chat_client = ChatClient(
            ChatClientConfig(
                base_url=settings.CHAT_API_BASE_URL,
                organisation_token=settings.CHAT_ORGANISATION_TOKEN,
            )
        )

    @swagger_auto_schema(
        method="post",
        manual_parameters=[
            openapi.Parameter(
                "project_id",
                openapi.IN_QUERY,
                description="Optional: Project ID for the chat room",
                type=openapi.TYPE_STRING,
                required=False,
            ),
        ],
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "name": openapi.Schema(type=openapi.TYPE_STRING),
                "group_avatar": openapi.Schema(
                    type=openapi.TYPE_STRING, nullable=True, format="uuid"
                ),
                "contact_ids": openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(
                        type=openapi.TYPE_STRING, format=openapi.FORMAT_UUID
                    ),
                ),
                "collaborator_ids": openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(
                        type=openapi.TYPE_STRING, format=openapi.FORMAT_UUID
                    ),
                ),
                "manual_participants": openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        properties={
                            "name": openapi.Schema(
                                type=openapi.TYPE_STRING, nullable=True
                            ),
                            "email": openapi.Schema(
                                type=openapi.TYPE_STRING,
                                format=openapi.FORMAT_EMAIL,
                            ),
                            "data": openapi.Schema(type=openapi.TYPE_OBJECT),
                        },
                        required=["email"],
                    ),
                ),
            },
        ),
        responses={
            200: ProjectRoomSerializer,
            201: ProjectRoomSerializer,
            400: "Invalid data provided",
            404: "Project not found",
            500: "Internal server error",
        },
    )
    @action(detail=False, methods=["post"], url_path="rooms")
    def create_project_room(self, request: Request) -> Response:  # noqa

        try:
            project_id = request.query_params.get("project_id")
            group_avatar = request.data.get("group_avatar")
            project = (
                None
                if not project_id
                else get_object_or_404(Project, id=project_id)
            )

            participants = []
            member_users = []

            # Add current user as first participant and member
            current_user = request.user
            member_users.append(current_user)
            participants.append(
                {
                    "name": current_user.get_full_name(),
                    "email": current_user.email,
                    "timezone": Setting.objects.get_user_timezone(
                        current_user
                    ),
                    "data": {
                        "user_id": str(current_user.id),
                        "profilePicture": getattr(
                            current_user, "profile_picture.url", None
                        )
                        if hasattr(current_user, "profile_picture")
                        else None,
                        "addressLine1": getattr(
                            current_user, "address_line_1", None
                        ),
                        "addressLine2": getattr(
                            current_user, "address_line_2", None
                        ),
                        "city": getattr(current_user, "city", None),
                        "state": getattr(current_user, "state", None),
                        "zipCode": getattr(current_user, "zip_code", None),
                        "country": getattr(current_user, "country", None),
                        "email": current_user.email,
                        "mobile": getattr(current_user, "mobile", None),
                        "extension": getattr(current_user, "extension", None),
                        "gender": getattr(current_user, "gender", None),
                    },
                }
            )

            # Add participants from user list
            collaborator_ids = request.data.get("collaborator_ids", [])
            if collaborator_ids:
                collaborators = User.objects.filter(id__in=collaborator_ids)
                for collaborator in collaborators:
                    member_users.append(collaborator)
                    participants.append(
                        {
                            "name": collaborator.get_full_name(),
                            "email": collaborator.email,
                            "timezone": Setting.objects.get_user_timezone(
                                collaborator
                            ),
                            "data": {
                                "user_id": str(collaborator.id),
                                "profilePicture": getattr(
                                    collaborator, "profile_picture.url", None
                                )
                                if hasattr(collaborator, "profile_picture")
                                else None,
                                "addressLine1": getattr(
                                    collaborator, "address_line_1", None
                                ),
                                "addressLine2": getattr(
                                    collaborator, "address_line_2", None
                                ),
                                "city": getattr(collaborator, "city", None),
                                "state": getattr(collaborator, "state", None),
                                "zipCode": getattr(
                                    collaborator, "zip_code", None
                                ),
                                "country": getattr(
                                    collaborator, "country", None
                                ),
                                "email": collaborator.email,
                                "mobile": getattr(
                                    collaborator, "mobile", None
                                ),
                                "extension": getattr(
                                    collaborator, "extension", None
                                ),
                                "gender": getattr(
                                    collaborator, "gender", None
                                ),
                            },
                        }
                    )

            # Add participants from contact list
            contact_ids = request.data.get("contact_ids", [])
            if contact_ids:
                contacts = Contact.objects.filter(id__in=contact_ids)
                for contact in contacts:
                    # Get or create user for contact
                    user, created = User.objects.get_or_create(
                        email=contact.email,
                        defaults={
                            "first_name": contact.first_name,
                            "last_name": contact.last_name,
                        },
                    )
                    member_users.append(user)

                    participants.append(
                        {
                            "name": f"{contact.first_name} {contact.last_name}",
                            "email": contact.email,
                            "timezone": Setting.objects.get_user_timezone(
                                user
                            ),
                            "data": {
                                "contact_id": str(contact.id),
                                "profilePicture": contact.profile_picture.url
                                if contact.profile_picture
                                else None,
                                "phone_number": contact.phone_number,
                                "extension": contact.extension,
                                "company": contact.company,
                                "addressLine1": contact.address_line_1,
                                "addressLine2": contact.address_line_2,
                                "city": contact.city,
                                "state": contact.state,
                                "zipCode": contact.zip_code,
                                "country": contact.country,
                            },
                        }
                    )

            # Add manually specified participants
            manual_participants = request.data.get("manual_participants", [])
            for participant in manual_participants:
                if "email" in participant:
                    # Get or create user for manual participant
                    email = participant["email"]
                    name_parts = participant.get("name", "").split(maxsplit=1)
                    first_name = name_parts[0] if name_parts else ""
                    last_name = name_parts[1] if len(name_parts) > 1 else ""

                    user, created = User.objects.get_or_create(
                        email=email,
                        defaults={
                            "first_name": first_name,
                            "last_name": last_name,
                        },
                    )
                    member_users.append(user)

                    participants.append(
                        {
                            "name": participant.get("name"),
                            "email": participant["email"],
                            "timezone": Setting.objects.get_user_timezone(
                                user
                            ),
                            "data": {**participant.get("data", {})},
                        }
                    )

            # Ensure we have at least one additional participant
            if len(participants) < 2:
                return Response(
                    {
                        "detail": "At least one additional participant is required."
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Set tags based on project_id presence
            tags = (
                [str(project.id)]
                if project
                else [str(user.id) for user in member_users]
            )
            users_to_notify = []

            if contact_ids or collaborator_ids:
                # serves as group chat and unique room for project  with  existing chat
                tags.extend(str(cid) for cid in (contact_ids or []))
                tags.extend(str(cid) for cid in (collaborator_ids or []))

                users_to_notify.extend(str(cid) for cid in (contact_ids or []))
                users_to_notify.extend(
                    str(cid) for cid in (collaborator_ids or [])
                )

            room_name = request.data.get("name", str(uuid.uuid4()))

            tags = list(set(tags))

            # Create room in external chat service
            room_data = {
                "name": room_name,
                "tags": tags,
                "participants": participants,
                "group_avatar": group_avatar,
            }

            try:
                chat_room = self.chat_client.create_room(room_data)

                # Try to create the room
                room = ProjectRoom.objects.create(
                    room_id=chat_room["id"],
                    project=project,
                    name=room_name,
                    created_by=request.user,
                )

                # Add all members to the room
                room.members.add(*member_users)

                response_status = status.HTTP_201_CREATED

            except Exception as e:
                if "duplicate key value" in str(e) and "room_id" in str(e):
                    # If room already exists, fetch it
                    try:
                        room = ProjectRoom.objects.get(
                            room_id=chat_room["id"], project=project
                        )

                        # Update members if necessary
                        existing_member_ids = set(
                            room.members.values_list("id", flat=True)
                        )
                        # Add any new members
                        members_to_add = [
                            user
                            for user in member_users
                            if user.id not in existing_member_ids
                        ]
                        if members_to_add:
                            room.members.add(*members_to_add)

                        response_status = status.HTTP_200_OK

                    except ProjectRoom.DoesNotExist:
                        logger.error(
                            f"Room with ID {chat_room['id']} exists in external service but not in database"
                        )
                        raise
                else:
                    raise

            serializer = ProjectRoomSerializer(
                room, context={"request": request}
            )

            users_to_notify.append(request.user.id)
            is_group_chat = len(users_to_notify) >= 3

            if is_group_chat:

                for user_to_notify in users_to_notify:
                    process_chat_notifications.delay(
                        room_id=str(room.room_id),
                        sender_id=user_to_notify,
                        message_content=f"{request.user.full_name} added you to {room.name}",
                    )

            return Response(serializer.data, status=response_status)

        except Project.DoesNotExist:
            return Response(
                {"detail": "Project not found."},
                status=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:
            return Response(
                {"detail": f"Failed to create chat room: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "project_id",
                openapi.IN_QUERY,
                description="Optional: Project ID to filter rooms",
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                "last_n_messages",
                openapi.IN_QUERY,
                description="Optional: Number of latest messages to retrieve",
                type=openapi.TYPE_INTEGER,
                required=False,
            ),
            openapi.Parameter(
                "project_name",
                openapi.IN_QUERY,
                description="Filter by project name",
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                "member_email",
                openapi.IN_QUERY,
                description="Filter by member email",
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                "member_name",
                openapi.IN_QUERY,
                description="Filter by member name",
                type=openapi.TYPE_STRING,
                required=False,
            ),
        ],
        responses={
            200: ProjectRoomSerializer(many=True),
            400: "Invalid filter parameters",
            404: "Project not found",
            500: "Internal server error",
        },
    )
    @action(detail=False, methods=["get"], url_path="rooms")
    def project_rooms(self, request: Request) -> Response:
        """Get all chat rooms with optional filtering."""
        try:
            rooms_query = ProjectRoom.objects.filter(
                members=request.user
            ).order_by("-updated_at")

            # Project ID filter (existing)
            project_id = request.query_params.get("project_id")
            if project_id:
                project = get_object_or_404(Project, id=project_id)
                rooms_query = rooms_query.filter(project=project)

            # Project name filter
            project_name = request.query_params.get("project_name")
            if project_name:
                rooms_query = rooms_query.filter(
                    project__name__icontains=project_name
                )

            # Member email filter
            member_email = request.query_params.get("member_email")
            if member_email:
                rooms_query = rooms_query.filter(
                    members__email__icontains=member_email
                )

            # Member name filter
            member_name = request.query_params.get("member_name")
            if member_name:
                rooms_query = rooms_query.filter(
                    Q(members__first_name__icontains=member_name)
                    | Q(members__last_name__icontains=member_name)
                )

            # Last N messages parameter (existing)
            last_n_messages = request.query_params.get("last_n_messages")
            if last_n_messages:
                try:
                    last_n_messages = int(last_n_messages)
                except ValueError:
                    return Response(
                        {"detail": "last_n_messages must be an integer"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

            # Use distinct to avoid duplicates from the member name query
            rooms_query = rooms_query.distinct()

            serializer = ProjectRoomSerializer(
                rooms_query,
                many=True,
                context={
                    "request": request,
                    "view": self,
                    "last_n_messages": last_n_messages,
                },
            )
            return Response(serializer.data)

        except ValueError as e:
            return Response(
                {"detail": f"Invalid filter parameters: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Project.DoesNotExist:
            return Response(
                {"detail": "Project not found."},
                status=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:
            return Response(
                {"detail": f"Failed to retrieve chat rooms: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        method="get",
        manual_parameters=[
            openapi.Parameter(
                "participant_id",
                openapi.IN_QUERY,
                description="Optional participant ID",
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                "last_n_messages",
                openapi.IN_QUERY,
                description="Optional: Number of latest messages to retrieve",
                type=openapi.TYPE_INTEGER,
                required=False,
            ),
        ],
        responses={
            200: RoomSerializer,
            403: "Forbidden - User is not a participant",
            500: "Internal server error",
        },
    )
    @action(detail=True, methods=["get"])
    def get_room(self, request: Request, room_id=None) -> Response:
        """Get details for a specific chat room with optional last N messages"""
        participant_id = request.query_params.get("participant_id")
        last_n_messages = request.query_params.get("last_n_messages")

        try:
            # First verify room participation and get current_participant_id
            room_check = self.chat_client.get_room(room_id=room_id)
            current_participant = next(
                (
                    p
                    for p in room_check.get("participants", [])
                    if p["email"] == request.user.email
                ),
                None,
            )

            if not current_participant:
                return Response(
                    {"detail": "User is not a participant in this chat room."},
                    status=status.HTTP_403_FORBIDDEN,
                )

            # Use current_participant_id if no participant_id was provided
            if not participant_id:
                participant_id = current_participant.get("id")

            if last_n_messages:
                try:
                    last_n_messages = int(last_n_messages)
                except ValueError:
                    return Response(
                        {"detail": "last_n_messages must be an integer"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

            # Delete any existing notifications for this room and user
            self.cleanup_item_notifications(
                item_id=room_id,
                categories=[Notification.Category.CHAT_MESSAGES],
            )

            # Get user timezone
            participant_timezone = Setting.objects.get_user_timezone(
                request.user
            )

            room = self.chat_client.get_room(
                room_id=room_id,
                participant_id=participant_id,
                last_n_messages=last_n_messages,
                participant_timezone=participant_timezone,
            )

            return Response(room)
        except Exception as e:
            logger.error(f"Failed to get chat room: {str(e)}")
            return Response(
                {"detail": f"Failed to get chat room: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        method="patch",
        request_body=RoomSerializer,
        responses={
            200: RoomSerializer,
            400: "Invalid data provided",
            404: "Room not found",
            500: "Internal server error",
        },
    )
    @action(detail=True, methods=["patch"])
    def update_room(self, request: Request, room_id=None) -> Response:
        try:
            # First, find the room in local database
            local_room = None
            group_avatar = request.data.get("group_avatar")
            try:
                local_room = ProjectRoom.objects.get(room_id=room_id)
            except ProjectRoom.DoesNotExist:
                return Response(
                    {"detail": "Room not found in local database."},
                    status=status.HTTP_404_NOT_FOUND,
                )

            # Get current room data from chat service
            room = self.chat_client.get_room(room_id=room_id)

            # Get the current tags from the room
            current_tags = room.get("tags", [])

            # Prepare update data
            update_data = {
                "name": request.data.get("name", room["name"]),
                "data": {
                    **room.get("data", {}),
                    **request.data.get("data", {}),
                },
                "tags": current_tags,
                "group_avatar": group_avatar,
            }

            # Update in chat service
            updated_room = self.chat_client.update_room(
                room_id=room_id, data=update_data
            )

            # Update local database
            if "name" in request.data:
                local_room.name = request.data["name"]
            if "is_archived" in request.data:
                local_room.is_archived = request.data["is_archived"]

            local_room.save()

            return Response(updated_room)

        except Exception as e:
            logger.error(f"Failed to update room: {str(e)}")
            return Response(
                {"detail": f"Failed to update room: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=True, methods=["delete"])
    def delete_room(self, request: Request, room_id=None) -> Response:
        """Delete a chat room from both local database and chat service."""
        if not room_id:
            return Response(
                {"detail": "Room ID is required."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # First, find the room in local database
        try:
            local_room = ProjectRoom.objects.get(room_id=room_id)
        except ProjectRoom.DoesNotExist:
            logger.warning(f"Room {room_id} not found in local database.")
            return Response(
                {"detail": "Room not found."},
                status=status.HTTP_404_NOT_FOUND,
            )

        # Check if user is participant in chat room
        try:
            room = self.chat_client.get_room(room_id=room_id)
            current_participant = next(
                (
                    p
                    for p in room.get("participants", [])
                    if p["email"] == request.user.email
                ),
                None,
            )

            if not current_participant:
                return Response(
                    {"detail": "User is not a participant in this chat room."},
                    status=status.HTTP_403_FORBIDDEN,
                )
        except Exception as e:
            logger.error(f"Failed to check room participation: {str(e)}")
            return Response(
                {"detail": "Failed to verify room participation."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

        success = True
        error_messages = []

        # Delete from chat service
        try:
            self.chat_client.delete_room(
                room_id=room_id, participant_id=current_participant["id"]
            )
            logger.info(
                f"Successfully deleted room {room_id} from chat service."
            )
        except Exception as e:
            success = False
            error_messages.append(
                f"Failed to delete room from chat service: {str(e)}"
            )
            logger.error(error_messages[-1])

        # Delete from local database
        try:
            local_room.delete()
            logger.info(
                f"Successfully deleted room {room_id} from local database."
            )
        except Exception as e:
            success = False
            error_messages.append(
                f"Failed to delete room from local database: {str(e)}"
            )
            logger.error(error_messages[-1])

        if success:
            return Response(status=status.HTTP_204_NO_CONTENT)
        return Response(
            {
                "detail": "Failed to completely delete room.",
                "errors": error_messages,
            },
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )

    @swagger_auto_schema(
        method="get",
        manual_parameters=[
            openapi.Parameter(
                "name",
                openapi.IN_QUERY,
                description="Optional: Search rooms by name",
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                "participant_email",
                openapi.IN_QUERY,
                description="Optional: Search rooms by participant email",
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_EMAIL,
                required=False,
            ),
        ],
        responses={
            200: RoomSerializer(many=True),
            400: "Invalid parameters",
            500: "Internal server error",
        },
    )
    @action(detail=False, methods=["get"], url_path="search")
    def search_rooms(self, request: Request) -> Response:
        """Search for chat rooms by name and / or participant email."""
        try:
            name = request.query_params.get("name")
            participant_email = request.query_params.get("participant_email")

            # Validate that at least one parameter is provided
            if not name and not participant_email:
                return Response(
                    {
                        "detail": "At least one search parameter (name or participant_email) is required"
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Search rooms using chat client
            rooms = self.chat_client.search_room(
                name=name, participant_email=participant_email
            )
            return Response(rooms)

        except Exception as e:
            logger.error(f"Failed to search chat rooms: {str(e)}")
            return Response(
                {"detail": f"Failed to search chat rooms: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        responses={
            200: ParticipantSerializer,
            404: "Participant not found",
            500: "Internal server error",
        },
    )
    @action(
        detail=False,
        methods=["get"],
        url_path="participant/(?P<participant_id>[^/.]+)",
    )
    def get_participant(
        self, request: Request, participant_id=None
    ) -> Response:
        """Get a participant by their ID."""
        try:
            participant = self.chat_client.get_participant(participant_id)
            return Response(participant)
        except Exception as e:
            return Response(
                {"detail": f"Failed to get participant: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        method="post",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "contact_ids": openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(
                        type=openapi.TYPE_STRING, format=openapi.FORMAT_UUID
                    ),
                ),
                "collaborator_ids": openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(
                        type=openapi.TYPE_STRING, format=openapi.FORMAT_UUID
                    ),
                ),
                "manual_participants": openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        properties={
                            "name": openapi.Schema(
                                type=openapi.TYPE_STRING,
                                nullable=True,
                            ),
                            "email": openapi.Schema(
                                type=openapi.TYPE_STRING,
                                format=openapi.FORMAT_EMAIL,
                            ),
                            "data": openapi.Schema(type=openapi.TYPE_OBJECT),
                        },
                        required=["email"],
                    ),
                ),
            },
        ),
        responses={
            200: ParticipantCreateAsListSerializer,
            400: "No valid participants provided",
            404: "Room not found",
            500: "Internal server error",
        },
    )
    @action(detail=True, methods=["post"])
    def add_participants(self, request: Request, room_id=None) -> Response:
        try:
            # Try to get either a ProjectRoom or GlobalRoom instance
            room = None
            try:
                room = ProjectRoom.objects.get(room_id=room_id)
            except ProjectRoom.DoesNotExist:
                return Response(
                    {"detail": "Room not found."},
                    status=status.HTTP_404_NOT_FOUND,
                )

            participants = []
            users_to_add = []

            # From contact list
            contact_ids = request.data.get("contact_ids", [])
            if contact_ids:
                contacts = Contact.objects.filter(id__in=contact_ids)
                for contact in contacts:
                    # Get or create user for each contact
                    user, created = User.objects.get_or_create(
                        email=contact.email,
                        defaults={
                            "first_name": contact.first_name,
                            "last_name": contact.last_name,
                        },
                    )
                    users_to_add.append(user)

                    participants.append(
                        {
                            "name": f"{contact.first_name} {contact.last_name}"
                            if contact.first_name or contact.last_name
                            else None,
                            "email": contact.email,
                            "timezone": Setting.objects.get_user_timezone(
                                user
                            ),
                            "data": {
                                "contact_id": str(contact.id),
                                "profilePicture": contact.profile_picture.url
                                if contact.profile_picture
                                else None,
                                "phone_number": contact.phone_number,
                                "extension": contact.extension,
                                "company": contact.company,
                                "addressLine1": contact.address_line_1,
                                "addressLine2": contact.address_line_2,
                                "city": contact.city,
                                "state": contact.state,
                                "zipCode": contact.zip_code,
                                "country": contact.country,
                            },
                        }
                    )

            # Add participants from user list
            collaborator_ids = request.data.get("collaborator_ids", [])
            if collaborator_ids:
                collaborators = User.objects.filter(id__in=collaborator_ids)
                for collaborator in collaborators:
                    users_to_add.append(collaborator)
                    participants.append(
                        {
                            "name": collaborator.get_full_name(),
                            "email": collaborator.email,
                            "timezone": Setting.objects.get_user_timezone(
                                collaborator
                            ),
                            "data": {
                                "user_id": str(collaborator.id),
                                "profilePicture": getattr(
                                    collaborator, "profile_picture.url", None
                                )
                                if hasattr(collaborator, "profile_picture")
                                else None,
                                "addressLine1": getattr(
                                    collaborator, "address_line_1", None
                                ),
                                "addressLine2": getattr(
                                    collaborator, "address_line_2", None
                                ),
                                "city": getattr(collaborator, "city", None),
                                "state": getattr(collaborator, "state", None),
                                "zipCode": getattr(
                                    collaborator, "zip_code", None
                                ),
                                "country": getattr(
                                    collaborator, "country", None
                                ),
                                "email": collaborator.email,
                                "mobile": getattr(
                                    collaborator, "mobile", None
                                ),
                                "extension": getattr(
                                    collaborator, "extension", None
                                ),
                                "gender": getattr(
                                    collaborator, "gender", None
                                ),
                            },
                        }
                    )

            # Manually added participants
            manual_participants = request.data.get("manual_participants", [])
            for participant in manual_participants:
                if "email" in participant:
                    email = participant["email"]
                    name_parts = participant.get("name", "").split(maxsplit=1)
                    first_name = name_parts[0] if name_parts else ""
                    last_name = name_parts[1] if len(name_parts) > 1 else ""

                    # Get or create user for manual participant
                    user, created = User.objects.get_or_create(
                        email=email,
                        defaults={
                            "first_name": first_name,
                            "last_name": last_name,
                        },
                    )
                    users_to_add.append(user)

                    participants.append(
                        {
                            "name": participant.get("name"),
                            "email": participant["email"],
                            "timezone": Setting.objects.get_user_timezone(
                                user
                            ),
                            "data": {**participant.get("data", {})},
                        }
                    )

            if not participants:
                return Response(
                    {"detail": "No valid participants provided."},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Add participants to chat room using chat client
            result = self.chat_client.add_participants(
                room_id=room_id, participants=participants
            )

            # Add users to room members
            room.members.add(*users_to_add)

            # Update tags if it's a global room (project is None)
            if room.project is None:
                current_room = self.chat_client.get_room(room_id=room_id)
                all_member_ids = [
                    str(user_id)
                    for user_id in room.members.values_list("id", flat=True)
                ]
                self.chat_client.update_room(
                    room_id=room_id,
                    data={**current_room, "tags": all_member_ids},
                )

            return Response(result)

        except Exception as e:
            logger.error(f"Failed to add participants: {str(e)}")
            return Response(
                {"detail": f"Failed to add participants: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        method="post",
        request_body=openapi.Schema(
            type=openapi.TYPE_ARRAY,
            items=openapi.Schema(
                type=openapi.TYPE_STRING, format=openapi.FORMAT_UUID
            ),
            description="List of participant UUIDs",
        ),
        responses={
            200: ParticipantCreateAsListSerializer,
            400: "No participant IDs provided",
            500: "Internal server error",
        },
    )
    @action(detail=True, methods=["post"])
    def add_participants_by_ids(self, request, room_id=None) -> Response:
        participant_ids = (
            request.data
        )  # Expecting a flat list of UUIDs directly
        if not isinstance(participant_ids, list) or not participant_ids:
            return Response(
                {"detail": "No participant IDs provided."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            # Convert IDs to strings and call the chat client
            result = self.chat_client.add_participants_by_ids(
                room_id=room_id,
                participant_ids=[str(pid) for pid in participant_ids],
            )

            # Ensure the response is formatted correctly for the serializer
            data = {
                "participants": result
                if isinstance(result, list)
                else [result]
            }

            serializer = ParticipantCreateAsListSerializer(data=data)
            if serializer.is_valid():
                return Response(serializer.data)
            return Response(
                serializer.errors, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        except Exception as e:
            return Response(
                {"detail": f"Failed to add participants by IDs: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        method="post",
        responses={
            200: "Participant removed successfully",
            400: "Invalid participant ID",
            404: "Room or participant not found",
            500: "Internal server error",
        },
    )
    @action(detail=True, methods=["post"])
    def remove_participant(
        self, request: Request, room_id=None, participant_id=None
    ) -> Response:

        if not participant_id:
            return Response(
                {"detail": "Participant ID is required."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            # Try to get either a ProjectRoom or GlobalRoom instance
            room = None
            try:
                room = ProjectRoom.objects.get(room_id=room_id)
            except ProjectRoom.DoesNotExist:
                return Response(
                    {"detail": "Room not found."},
                    status=status.HTTP_404_NOT_FOUND,
                )

            # First get the participant details to get their email

            participant = self.chat_client.get_participant(str(participant_id))

            removed_user = None

            # Find the user by email and remove from members

            if participant and "email" in participant:

                try:
                    removed_user = User.objects.get(email=participant["email"])
                    room.members.remove(removed_user)
                except User.DoesNotExist:
                    logger.warning(
                        f"User with email {participant['email']} not found in database"
                    )

            # Remove participant from chat room using chat client

            result = self.chat_client.remove_participant(
                room_id=room_id, participant_id=participant_id
            )

            # Update tags if it's a global room (project is None) and user was removed
            if room.project is None and removed_user:
                current_room = self.chat_client.get_room(room_id=room_id)
                updated_member_ids = [
                    str(user_id)
                    for user_id in room.members.values_list("id", flat=True)
                ]
                self.chat_client.update_room(
                    room_id=room_id,
                    data={**current_room, "tags": updated_member_ids},
                )

            if removed_user:
                process_chat_notifications.delay(
                    room_id=str(room.room_id),
                    sender_id=str(removed_user.id),
                    message_content=f"{removed_user.full_name} removed you from {room.name}",
                )

            return Response(result)

        except Exception as e:
            logger.error(f"Failed to remove participant: {str(e)}")
            return Response(
                {"detail": f"Failed to remove participant: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=True, methods=["get"])
    def participants(self, request: Request, room_id=None) -> Response:
        try:
            room = self.chat_client.get_room(room_id=room_id)
            page = int(request.query_params.get("page", 1))
            size = int(request.query_params.get("size", 50))
            participants = self.chat_client.get_participants(
                room_id=room["id"], page=page, size=size
            )
            return Response(participants)
        except Exception as e:
            return Response(
                {"detail": f"Failed to get participants: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "content": openapi.Schema(
                    type=openapi.TYPE_STRING, description="Message content"
                ),
                "attachments": openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(
                        type=openapi.TYPE_STRING,
                        description="Attachment ID",
                        format="uuid",
                    ),
                ),
            },
            anyOf=[{"required": ["content"]}, {"required": ["attachments"]}],
        ),
        responses={
            200: ChatSerializers,
            400: "Either message content or attachments are required",
            403: "User is not a participant in this chat room",
            500: "Internal server error",
        },
    )
    @action(detail=True, methods=["post"])
    def send_message(self, request: Request, room_id=None) -> Response:
        content = request.data.get("content", "")
        attachment_ids = request.data.get("attachments", [])

        try:
            project_room = ProjectRoom.objects.get(room_id=room_id)

            if not content and not attachment_ids:
                return Response(
                    {
                        "detail": "Either message content or attachments are required."
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            room = self.chat_client.get_room(room_id=room_id)
            current_participant = next(
                (
                    p
                    for p in room.get("participants", [])
                    if p["email"] == request.user.email
                ),
                None,
            )

            if not current_participant:
                return Response(
                    {"detail": "User is not a participant in this chat room."},
                    status=status.HTTP_403_FORBIDDEN,
                )

            message = self.chat_client.create_chat(
                {
                    "content": content,
                    "room_id": room["id"],
                    "participant_id": current_participant["id"],
                    "created_at": datetime.now().isoformat(),
                    "attachments": attachment_ids,
                }
            )

            project_room.save(update_fields=["updated_at"])

            for member in project_room.members.all():
                process_chat_notifications.delay(
                    room_id=str(room["id"]),
                    sender_id=str(member.id),
                    message_content=content,
                )

            return Response(message)

        except ProjectRoom.DoesNotExist:
            return Response(
                {"detail": "Room not found."},
                status=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:
            return Response(
                {"detail": f"Failed to send message: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        method="patch",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "content": openapi.Schema(
                    type=openapi.TYPE_STRING, description="Message content"
                ),
                "attachments": openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(
                        type=openapi.TYPE_STRING,
                        description="Attachment ID",
                        format="uuid",
                    ),
                ),
            },
            required=["content"],
        ),
        responses={
            200: ChatSerializers,
            400: "Message content is required",
            403: "User is not a participant in this chat room",
            500: "Internal server error",
        },
    )
    @action(detail=False, methods=["patch"])
    def update_message(
        self, request: Request, room_id: uuid.UUID, chat_id: uuid.UUID
    ) -> Response:
        """Update a chat message."""
        content = request.data.get("content")
        if not content:
            return Response(
                {"detail": "Message content is required."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            room = self.chat_client.get_room(room_id=str(room_id))
            participant_id = next(
                (
                    p.get("id")
                    for p in room.get("participants", [])
                    if p["email"] == request.user.email
                ),
                None,
            )
            if not participant_id:
                return Response(
                    {"detail": "User is not a participant in this chat room."},
                    status=status.HTTP_403_FORBIDDEN,
                )

            attachment_ids = request.data.get("attachments", [])
            updated_message = self.chat_client.update_chat(
                str(chat_id),
                {
                    "content": content,
                    "room_id": room["id"],
                    "participant_id": participant_id,
                    "attachments": attachment_ids,
                    "updated_at": datetime.now().isoformat(),
                },
            )
            return Response(updated_message)
        except Exception as e:
            return Response(
                {"detail": f"Failed to update message: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        method="delete",
        responses={
            204: "Chat deleted successfully",
            404: "Chat not found",
            500: "Internal server error",
        },
    )
    @action(detail=False, methods=["delete"])
    def delete_message(
        self, request: Request, room_id: uuid.UUID, chat_id: uuid.UUID
    ) -> Response:
        """Delete a chat message."""
        try:
            # Get the room to verify participant
            room = self.chat_client.get_room(room_id=str(room_id))

            # Check if user is participant
            is_participant = any(
                p["email"] == request.user.email
                for p in room.get("participants", [])
            )

            if not is_participant:
                return Response(
                    {"detail": "User is not a participant in this chat room."},
                    status=status.HTTP_403_FORBIDDEN,
                )

            # If participant check passes, delete the message
            self.chat_client.delete_chat(str(chat_id))
            return Response(status=status.HTTP_204_NO_CONTENT)
        except Exception as e:
            return Response(
                {"detail": f"Failed to delete chat: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=True, methods=["get"])
    def get_messages(self, request: Request, room_id=None) -> Response:
        """
        Retrieve messages from a chat room with pagination support.
        """
        try:
            # Get room and validate participant
            room = self.chat_client.get_room(room_id=room_id)
            current_participant = next(
                (
                    p
                    for p in room.get("participants", [])
                    if p["email"] == request.user.email
                ),
                None,
            )

            if not current_participant:
                return Response(
                    {"detail": "User is not a participant in this chat room."},
                    status=status.HTTP_403_FORBIDDEN,
                )

            # Get query parameters
            order = request.query_params.get("order", "desc")

            messages = self.chat_client.get_chats_in_room(
                room_id=room_id,
                participant_id=current_participant["id"],
                order=order,
            )
            return Response(messages)
        except Exception as e:
            return Response(
                {"detail": f"Failed to retrieve messages: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=True, methods=["get"])
    def get_unread_messages(
        self, request: Request, participant_id=None
    ) -> Response:
        """
        Retrieve unread messages for the requesting participant in a chat room.
        """
        try:
            unread_messages = self.chat_client.get_unread_messages(
                participant_id=participant_id
            )
            return Response(unread_messages)
        except Exception as e:
            return Response(
                {"detail": f"Failed to retrieve unread messages: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        method="get",
        manual_parameters=[
            openapi.Parameter(
                "content",
                openapi.IN_QUERY,
                description="Search for chat content",
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                "participant_email",
                openapi.IN_QUERY,
                description="Filter by participant email",
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_EMAIL,
                required=False,
            ),
        ],
        responses={
            200: ChatSerializers(many=True),
            403: "User is not a participant in this chat room",
            404: "Room not found",
            500: "Internal server error",
        },
    )
    @action(detail=True, methods=["get"], url_path="search")
    def search_messages(self, request: Request, room_id=None) -> Response:
        """Search messages within a specific chat room."""
        try:
            content = request.query_params.get("content")
            participant_email = request.query_params.get("participant_email")

            # Verify room exists and user is a participant
            room = self.chat_client.get_room(room_id=room_id)
            participant_id = next(
                (
                    p.get("id")
                    for p in room.get("participants", [])
                    if p["email"] == request.user.email
                ),
                None,
            )
            if not participant_id:
                return Response(
                    {"detail": "User is not a participant in this chat room."},
                    status=status.HTTP_403_FORBIDDEN,
                )

            # Search messages using chat client
            messages = self.chat_client.search_chat(
                id=room_id,
                participant_id=participant_id,
                content=content,
                participant_email=participant_email,
                order="desc",
            )

            return Response(messages)

        except Exception as e:
            return Response(
                {"detail": f"Failed to search messages: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        method="post",
        request_body=CreateAttachmentSerializer(many=True),
        responses={
            201: AttachmentSerializer(many=True),
            400: "Invalid data provided",
            500: "Internal server error",
        },
    )
    @action(detail=False, methods=["post"])
    def create_attachment(self, request: Request) -> Response:
        """Create new attachments."""
        try:
            attachments = self.chat_client.create_attachment(request.data)
            return Response(attachments, status=status.HTTP_201_CREATED)
        except Exception as e:
            return Response(
                {"detail": f"Failed to create attachment: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        responses={
            200: AttachmentSerializer,
            404: "Attachment not found",
            500: "Internal server error",
        },
    )
    @action(
        detail=False,
        methods=["get"],
        url_path="attachment/(?P<attachment_id>[^/.]+)",
    )
    def get_attachment(self, request: Request, attachment_id=None) -> Response:
        """Get a presigned URL for an attachment."""
        try:
            attachment = self.chat_client.generate_presigned_url(attachment_id)
            return Response(attachment)
        except Exception as e:
            return Response(
                {"detail": f"Failed to get attachment: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["delete"],
        url_path="attachment/(?P<attachment_id>[^/.]+)",
    )
    def delete_attachment(
        self, request: Request, attachment_id=None
    ) -> Response:
        """Delete an attachment."""
        try:
            self.chat_client.delete_attachment(attachment_id)
            return Response(status=status.HTTP_204_NO_CONTENT)
        except Exception as e:
            return Response(
                {"detail": f"Failed to delete attachment: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @swagger_auto_schema(
        method="post",
        responses={
            200: openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    "token": openapi.Schema(type=openapi.TYPE_STRING),
                    "expires_at": openapi.Schema(
                        type=openapi.TYPE_STRING, format="date-time"
                    ),
                },
            ),
            404: "Participant not found",
            500: "Internal server error",
        },
    )
    @action(detail=False, methods=["post"])
    def generate_participant_token(
        self, request: Request, participant_id: uuid.UUID
    ) -> Response:
        """Generate an authentication token for a chat participant."""
        try:
            token_data = self.chat_client.generate_token_for_participant(
                participant_id
            )
            return Response(token_data)
        except Exception as e:
            return Response(
                {"detail": f"Failed to generate token: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
