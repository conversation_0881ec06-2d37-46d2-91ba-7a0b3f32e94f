import os
from datetime import timed<PERSON><PERSON>
from pathlib import Path
from socket import gethostbyname
from socket import gethostname

import sentry_sdk
from dotenv import dotenv_values
from sentry_sdk.integrations.django import DjangoIntegration


# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


config = {
    **dotenv_values(".env"),  # load sensitive variables
    **os.environ,  # override loaded values with environment variables
}


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = (
    "django-insecure-(^s^cq_6&y6z071)c%kj^b7!12fdz^!8*s1z5jv(sw3loub$47"
)

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = config.get("DEBUG") == "True"

TEST_DEBUG = False
APP_DOMAIN = config.get(
    "DOMAIN",
)
IS_TESTING_ENVIRONMENT = "testing" in f"{APP_DOMAIN}".lower()

if not DEBUG:
    sentry_sdk.init(
        dsn="https://<EMAIL>/4505727843893248",
        integrations=[DjangoIntegration()],
        # If you wish to associate users to errors (assuming you are using
        # django.contrib.auth) you may enable sending PII data.
        send_default_pii=True,
        # Set traces_sample_rate to 1.0 to capture 100%
        # of transactions for performance monitoring.
        # We recommend adjusting this value Pin production.
        traces_sample_rate=1.0,
        # To set a uniform sample rate
        # Set profiles_sample_rate to 1.0 to profile 100%
        # of sampled transactions.
        # We recommend adjusting this value in production,
        profiles_sample_rate=1.0,
        environment=APP_DOMAIN,
    )


ALLOWED_HOSTS = [
    "127.0.0.1",
    "localhost",
    "testserver",  # Required for testing
    "**************",
    APP_DOMAIN,
    *config.get("ALLOWED_HOSTS", "").split(","),
]

if not DEBUG:
    ALLOWED_HOSTS.append(gethostbyname(gethostname()))

EMAIL_BACKEND = (
    "django.core.mail.backends.console.EmailBackend"
    if config.get("EMAIL_TEST_SUPPRESSION", "FALSE") == "True"
    else "django.core.mail.backends.smtp.EmailBackend"
)
EMAIL_USE_TLS = True
EMAIL_USE_SSL = False
EMAIL_HOST = config.get("EMAIL_HOST")
EMAIL_PORT = config.get("EMAIL_PORT")
EMAIL_HOST_USER = config.get("EMAIL_HOST_USER")
EMAIL_HOST_PASSWORD = config.get("EMAIL_HOST_PASSWORD")

ZOHO_SUPPORT_EMAIL = config.get("ZOHO_SUPPORT_EMAIL")
ZOHO_EMAIL = config.get("ZOHO_EMAIL")
ZOHO_KEY = config.get("ZOHO_KEY")
ZOHO_AUTH_TOKEN = config.get("ZOHO_AUTH_TOKEN")

SENDGRID_KEY = config.get("SENDGRID_KEY")

# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    # Third party apps
    "django_extensions",
    "rest_framework",
    "drf_yasg",
    "corsheaders",
    "rest_framework_simplejwt",
    "django_celery_beat",
    "import_export",
    "django_better_admin_arrayfield",
    "auditlog",
    "django_user_agents",
    "silk",
    # Tuulbox apps
    "authentication",
    "accounts",
    "company",
    "resources",
    "api",
    "storage",
    "project",
    "contacts",
    "communication",
    "integrations",
    "general",
    "search_engine",
    "notifications",
    "calendar_app",
    "recent_app",
    "literals",
    "backoffice",
    "chat",
]

DEBUGING_MIDDLEWARES = [
    "core.middleware.silk_auth_middleware.SilkStaffAccessMiddleware",
    "silk.middleware.SilkyMiddleware",
]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    *(DEBUGING_MIDDLEWARES if not TEST_DEBUG else []),
    "core.middleware.last_login_ middleware.UpdateLastActivityMiddleware",
    "auditlog.middleware.AuditlogMiddleware",
    "backoffice.auditlog_middleware.SetAuditLogActorMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "django_user_agents.middleware.UserAgentMiddleware",
    "crum.CurrentRequestUserMiddleware",
]


ROOT_URLCONF = "core.urls"
AUTH_USER_MODEL = "accounts.User"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [os.path.join(BASE_DIR, "html")],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "core.wsgi.application"


# Database
# https://docs.djangoproject.com/en/3.2/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql_psycopg2",
        "NAME": config.get("DATABASE_NAME"),
        "USER": config.get("DATABASE_USER"),
        "PASSWORD": config.get("DATABASE_PASSWORD"),
        "HOST": config.get("DATABASE_HOST"),
        "PORT": config.get("DATABASE_PORT"),
    }
}

# Password validation
# https://docs.djangoproject.com/en/3.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/3.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_L10N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.2/howto/static-files/

STATIC_URL = "/static/"
STATICFILES_DIRS = [os.path.join(BASE_DIR, "static")]
STATIC_ROOT = os.path.join(BASE_DIR, "staticfiles")

FILE_MAX_SIZE = int(config.get("FILE_MAX_SIZE", 1024))
FILE_UPLOAD_STORAGE = config.get("FILE_UPLOAD_STORAGE", "local")  # local | s3


IS_USING_LOCAL_STORAGE = FILE_UPLOAD_STORAGE == "local"


PUBLIC_MEDIA_LOCATION = "media/public/"
PRIVATE_MEDIA_LOCATION = "media/private/"
MEDIA_ROOT_NAME = "media"
FILEFIELD_MAX_LENGTH = int(config.get("FILEFIELD_MAX_LENGTH", 100000))


if TEST_DEBUG:
    PUBLIC_MEDIA_LOCATION = "media/test/public/"
    PRIVATE_MEDIA_LOCATION = "media/test/private/"


STATIC_LOCATION = "static/"


if FILE_UPLOAD_STORAGE == "local":
    MEDIA_ROOT = os.path.join(BASE_DIR, MEDIA_ROOT_NAME)
    MEDIA_URL = f"/{MEDIA_ROOT_NAME}/"

else:

    AWS_ACCESS_KEY_ID = config["AWS_ACCESS_KEY_ID"]
    AWS_SECRET_ACCESS_KEY = config["AWS_SECRET_ACCESS_KEY"]
    AWS_STORAGE_BUCKET_NAME = config["AWS_STORAGE_BUCKET_NAME"]
    AWS_S3_REGION_NAME = config["AWS_S3_REGION_NAME"]
    AWS_S3_CUSTOM_DOMAIN = config["AWS_S3_CUSTOM_DOMAIN"]
    AWS_S3_FILE_OVERWRITE = False
    AWS_DEFAULT_ACL = "public-read"
    AWS_S3_OBJECT_PARAMETERS = {"CacheControl": "max-age=86400"}

    if not DEBUG or True:
        STATIC_URL = f"https://{AWS_S3_CUSTOM_DOMAIN}/{STATIC_LOCATION}"
        STATICFILES_STORAGE = "core.storage_backends.StaticStorage"

    MEDIA_URL = f"https://{AWS_S3_CUSTOM_DOMAIN}/{MEDIA_ROOT_NAME}/"
    DEFAULT_FILE_STORAGE = "core.storage_backends.PublicMediaStorage"
    PRIVATE_FILE_STORAGE = "core.storage_backends.PrivateMediaStorage"

    FILE_UPLOAD_STORAGE = config.get("FILE_UPLOAD_STORAGE", "s3")
    AWS_PRESIGNED_EXPIRY = int(config.get("AWS_PRESIGNED_EXPIRY", 10))
    AWS_QUERYSTRING_EXPIRE = AWS_PRESIGNED_EXPIRY
    FILE_MAX_SIZE = int(config.get("FILE_MAX_SIZE", 1024))


# Default primary key field type
# https://docs.djangoproject.com/en/3.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"


CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_CREDENTIALS = True

CSRF_TRUSTED_ORIGINS = [
    "https://127.0.0.1",
    "http://localhost",
]

# Add FRONTEND_URL if it has a scheme
frontend_url = config.get("FRONTEND_URL", "")
if frontend_url and (
    frontend_url.startswith("http://") or frontend_url.startswith("https://")
):
    CSRF_TRUSTED_ORIGINS.append(frontend_url)

# Add any additional trusted origins from config
additional_origins = config.get(
    "CSRF_TRUSTED_ORIGINS", "http://localhost"
).split(",")
for origin in additional_origins:
    if origin and (
        origin.startswith("http://") or origin.startswith("https://")
    ):
        CSRF_TRUSTED_ORIGINS.append(origin)
GMAIL_TOPIC_NAME = config.get("GMAIL_TOPIC_NAME", "")


# Rest Frame work
REST_FRAMEWORK = {
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "rest_framework_simplejwt.authentication.JWTAuthentication",
        "rest_framework.authentication.BasicAuthentication",
        "rest_framework.authentication.SessionAuthentication",
    ],
    "DEFAULT_RENDERER_CLASSES": (
        "core.utils.renders.CustomJsonRender",
        "djangorestframework_camel_case.render.CamelCaseBrowsableAPIRenderer",
    ),
    "DEFAULT_PARSER_CLASSES": (
        # If you use MultiPartFormParser or FormParser, we also have a camel case version
        "djangorestframework_camel_case.parser.CamelCaseFormParser",
        "djangorestframework_camel_case.parser.CamelCaseMultiPartParser",
        "djangorestframework_camel_case.parser.CamelCaseJSONParser",
        # Any other parsers
    ),
    "DEFAULT_VERSIONING_CLASS": "rest_framework.versioning.URLPathVersioning",
    "DEFAULT_VERSION": "v1",
    "ALLOWED_VERSIONS": ["v1", "v2"],
    "VERSION_PARAM": "version",
    "EXCEPTION_HANDLER": "core.utils.exception_handler.custom_exception_handler",
    "DEFAULT_PAGINATION_CLASS": "rest_framework.pagination.PageNumberPagination",
    "PAGE_SIZE": config.get("PAGE_SIZE", 50),
}
# swagger
# https://drf-yasg.readthedocs.io/en/stable/readme.html
SWAGGER_SETTINGS = {
    "SECURITY_DEFINITIONS": {
        "Bearer": {"type": "apiKey", "name": "Authorization", "in": "header"},
    },
}

SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(days=1),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=90),
    "AUTH_HEADER_TYPES": ("Bearer",),
}

FIREBASE_CRED_FILE = Path.joinpath(
    BASE_DIR, config.get("TUULBOX_FIREBASE_CREDENTIALS", "")
)

#  GOOGLE INTEGRATION
GOOGLE_INTEGRATION_REDIRECT_URI = config.get(
    "GOOGLE_INTEGRATION_REDIRECT_URI", ""
)

GOOGLE_ANALYTICS_PROPERTY_ID = config.get("GOOGLE_ANALYTICS_PROPERTY_ID")

GRAPH_MODELS = {
    "all_applications": True,
    # "group_models": True,
    "exclude_models": [
        "LogEntry",
        "Group",
        "Permission",
        "ContentType",
        "Session",
        "*Abstract*",
    ],
}

SLACK_TOKEN = config.get("SLACK_TOKEN")

LOGS_CHANNEL_ID = config.get("LOGS_CHANNEL_ID")

TEST_RUNNER = "testing.test_runner.CustomTestRunner"

# Celery settings
CELERY_BROKER_URL = config.get("CELERY_BROKER_URL")
CELERY_RESULT_BACKEND = config.get("CELERY_BROKER_URL")
CELERY_BEAT_SCHEDULER = "django_celery_beat.schedulers:DatabaseScheduler"


INTEGRATION_SUCCESS_URL = config.get("INTEGRATION_SUCCESS_URL")

FRONTEND_URL = config.get("FRONTEND_URL", "https://web.tuulbox.app/")

APP_STORE_APP_URL = config.get(
    "APP_STORE_APP_URL", "https://apps.apple.com/us/app/tuulbox/id6463489669"
)
PLAY_STORE_APP_URL = config.get(
    "PLAY_STORE_APP_URL",
    "https://play.google.com/store/apps/details?id=com.tuulbox.app&hl=en&gl=US",
)

DEFAULT_REDIRECT_URL = "https://mobile.tuulbox.app/success"

CHAT_API_BASE_URL = config.get("CHAT_API_BASE_URL")

CHAT_ORGANISATION_TOKEN = config.get("CHAT_ORGANISATION_TOKEN")


CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": config.get(
            "CELERY_BROKER_URL"
        ),  # Local Link provided by the redis-server command
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        },
    }
}

USER_AGENTS_CACHE = "default"


# Cache timeouts (in seconds)
PROJECT_CACHE_TIMEOUT = int(
    os.environ.get("PROJECT_CACHE_TIMEOUT", 30 * 24 * 60 * 60)
)
RECENT_ACTIVITY_CACHE_TIMEOUT = int(
    os.environ.get("RECENT_ACTIVITY_CACHE_TIMEOUT", 30 * 60)
)
NOTIFICATION_CACHE_TIMEOUT = int(
    os.environ.get("NOTIFICATION_CACHE_TIMEOUT", 30 * 60)
)


NOTEBOOK_ARGUMENTS = [
    "--allow-root",
    "--port=8888",
    "--ip=0.0.0.0",
    "--NotebookApp.token=''",
    "--NotebookApp.password=''",
]
AUDITLOG_INCLUDE_ALL_MODELS = True


AUDITLOG_EXCLUDE_TRACKING_MODELS = ("django_celery_beat", "import_export")

if not TEST_DEBUG:

    # Django Silk Configuration
    SILKY_PYTHON_PROFILER = True
    SILKY_PYTHON_PROFILER_BINARY = True
    SILKY_PYTHON_PROFILER_RESULT_PATH = os.path.join(BASE_DIR, "profiling")
    SILKY_ANALYZE_QUERIES = True
    SILKY_META = True
    SILKY_INTERCEPT_PERCENT = 100  # Log all requests
    SILKY_AUTHENTICATION = True  # User must be logged in
    SILKY_AUTHORISATION = True  # User must have permissions

    # Only staff users can access the Silk interface
    def SILKY_PERMISSIONS(user):
        return user.is_staff and user.is_authenticated


EMAIL_RESEND_COOLDOWN_SECONDS = int(
    config.get("EMAIL_RESEND_COOLDOWN_SECONDS", 60)
)


CONTACT_CACHE_TIMEOUT = int(
    os.environ.get("CONTACT_CACHE_TIMEOUT", 30 * 24 * 60 * 60)
)
INSURANCE_CACHE_TIMEOUT = int(
    os.environ.get("INSURANCE_CACHE_TIMEOUT", 30 * 24 * 60 * 60)
)
LICENSE_CACHE_TIMEOUT = int(
    os.environ.get("LICENSE_CACHE_TIMEOUT", 30 * 24 * 60 * 60)
)
